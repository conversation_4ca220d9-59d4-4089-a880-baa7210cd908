<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Subtle Neon Login</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      background: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      overflow: hidden;
    }

    .login-background {
      position: fixed;
      top: -100%;
      left: -100%;
      width: 300%;
      height: 300vh;
      background: conic-gradient(
        from 0deg,
        rgb(255, 255, 255),
        rgba(0, 204, 255, 0.3),
        rgb(255, 255, 255),
        rgba(0, 204, 255, 0.3),
        rgb(255, 255, 255),
        rgba(0, 204, 255, 0.3),
        rgb(255, 255, 255),
        rgba(0, 204, 255, 0.3),
        rgb(255, 255, 255)
      );
      animation: rotate-lights 8s linear infinite;
    }

    .login-container {
      background: rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
      padding: 2.5rem;
      border-radius: 16px;
      border: 2px solid rgb(0, 217, 255, 0.253);
      box-shadow: 0 0 50px rgba(0, 123, 255, 0.589), 0 0 30px rgba(0, 123, 255, 0.2);
      width: 350px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    @keyframes rotate-lights {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    .login-container h2 {
      margin-bottom: 1.5rem;
      color: #00ccff;
      font-size: 2rem;
      text-shadow: 0 0 5px rgba(0, 204, 255, 0.5);
    }

    .login-container input {
      width: 100%;
      padding: 0.75rem;
      margin: 1rem 0;
      border: 1px solid rgba(0, 204, 255, 0.5);
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.05);
      color: white;
      font-size: 1rem;
      outline: none;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .login-container input:focus {
      border-color: rgba(0, 225, 255, 0.7);
      box-shadow: 0 0 8px rgba(0, 174, 255, 0.3);
    }

    .login-container button {
      width: 100%;
      padding: 0.75rem;
      background: linear-gradient(45deg, rgba(0, 204, 255, 0.8), rgba(0, 255, 204, 0.8));
      color: #0a0a0a;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: bold;
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .login-container button:hover {
      transform: translateY(-2px);
      box-shadow: 0 0 10px rgba(0, 204, 255, 0.5), 0 0 20px rgba(0, 255, 204, 0.5);
    }

    .login-container button:active {
      transform: translateY(0);
    }

    .login-container p {
      margin-top: 1.5rem;
      color: #666;
    }

    .login-container a {
      color: #00ccff;
      text-decoration: none;
      font-weight: bold;
      text-shadow: 0 0 3px rgba(0, 204, 255, 0.5);
    }

    .login-container a:hover {
      text-decoration: underline;
    }
  </style>
</head >
<body >
    <div class="login-background">
    </div>
    <div class="login-container">
        <h2>Welcome Back!</h2>
        <input type="text" placeholder="Email or Username">
        <input type="password" placeholder="Password">
        <button onclick="login()">Login</button>
        <p>Don't have an account? <a href="#">Sign up</a></p>
    </div>
</body>
</html>