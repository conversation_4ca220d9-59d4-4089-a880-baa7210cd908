/* Teacher Dashboard Styles */

/* Main Layout */
body {
  background-color: #0f172a;
  color: #e2e8f0;
}

.teacher-dashboard {
  min-height: 100vh;
  padding: 1rem;
}

/* Header */
.dashboard-header {
  background-color: #1e293b;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  margin-bottom: 1.5rem;
}

.dashboard-logo {
  color: #38bdf8;
  font-size: 1.5rem;
  font-weight: 700;
}

.dashboard-nav {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.dashboard-nav-link {
  color: #94a3b8;
  font-weight: 500;
  transition: color 0.2s;
}

.dashboard-nav-link:hover,
.dashboard-nav-link.active {
  color: #e2e8f0;
}

/* Welcome Banner */
.welcome-banner {
  background: linear-gradient(to right, #0284c7, #7c3aed);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  color: white;
}

/* Stats Cards */
.stats-card {
  background-color: #1e293b;
  border-radius: 0.75rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s;
}

.stats-card:hover {
  transform: translateY(-5px);
}

.stats-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-icon.primary {
  background-color: rgba(56, 189, 248, 0.2);
  color: #38bdf8;
}

.stats-icon.secondary {
  background-color: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.stats-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.stats-label {
  color: #94a3b8;
  font-size: 0.875rem;
  margin: 0;
}

/* Section Cards */
.section-card {
  background-color: #1e293b;
  border-radius: 0.75rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.section-header {
  background: linear-gradient(to right, #0284c7, #7c3aed);
  padding: 1rem 1.5rem;
  color: white;
  font-weight: 600;
  font-size: 1.125rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-content {
  padding: 1.5rem;
}

/* Action Cards */
.action-card {
  background-color: #334155;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: background-color 0.2s;
}

.action-card:hover {
  background-color: #475569;
}

.action-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon.primary {
  background-color: rgba(56, 189, 248, 0.2);
  color: #38bdf8;
}

.action-icon.secondary {
  background-color: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

/* Table Action Icons */
td .action-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}

td .action-icon:hover {
  transform: scale(1.15);
}

.action-title {
  font-weight: 600;
  margin: 0;
  color: #e2e8f0;
}

.action-description {
  color: #94a3b8;
  font-size: 0.875rem;
  margin: 0;
}

/* Tips Section */
.tip-card {
  background-color: #334155;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.tip-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.tip-text {
  color: #cbd5e1;
  font-size: 0.875rem;
  margin: 0;
}

/* Buttons */
.btn-primary {
  background-color: #0284c7;
  color: white;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #0369a1;
}

.btn-secondary {
  background-color: #7c3aed;
  color: white;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #6d28d9;
}

.btn-white {
  background-color: white;
  color: #0284c7;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.btn-white:hover {
  background-color: #f1f5f9;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.pagination > div {
  display: flex;
  gap: 0.5rem;
}

.pagination .relative {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  padding: 0 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.pagination .relative:not(.text-gray-400):not(.bg-primary-600) {
  background-color: #334155;
  color: #e2e8f0;
}

.pagination .relative:not(.text-gray-400):not(.bg-primary-600):hover {
  background-color: #475569;
}

.pagination .relative.bg-primary-600 {
  background-color: #0284c7;
  color: white;
}

.pagination .relative.text-gray-400 {
  color: #94a3b8;
  cursor: not-allowed;
}

.pagination svg {
  width: 1rem;
  height: 1rem;
}
