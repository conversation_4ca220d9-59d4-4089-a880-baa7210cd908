/**
 * Modern Face Recognition System Styles
 */

:root {
  --primary: #4361ee;
  --primary-dark: #3a56d4;
  --primary-light: #eef2ff;
  --secondary: #2ec4b6;
  --secondary-dark: #21a99d;
  --success: #06d6a0;
  --success-light: #e3fcf7;
  --danger: #ef476f;
  --danger-light: #ffedf2;
  --warning: #ffd166;
  --warning-light: #fff8e6;
  --info: #118ab2;
  --info-light: #e6f6fb;
  --dark: #073b4c;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 15px 25px rgba(0, 0, 0, 0.1);
  --radius-sm: 0.25rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-full: 9999px;
  --transition: all 0.3s ease;
}

/* Main container */
.modern-face-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 2rem;
}

/* Card styles */
.modern-card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  position: relative;
}

.modern-card-header {
  padding: 2rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  position: relative;
}

.modern-card-header h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.modern-card-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.modern-card-body {
  padding: 2rem;
}

/* Steps navigation */
.modern-steps {
  display: flex;
  justify-content: space-between;
  margin: -1.5rem 0 2rem;
  position: relative;
  z-index: 10;
}

.modern-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.modern-step-circle {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: var(--radius-full);
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--gray-600);
  box-shadow: var(--shadow);
  border: 2px solid var(--gray-300);
  transition: var(--transition);
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 2;
}

.modern-step.active .modern-step-circle {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
  transform: scale(1.1);
  box-shadow: 0 0 0 5px rgba(67, 97, 238, 0.2);
}

.modern-step.completed .modern-step-circle {
  background-color: var(--success);
  color: white;
  border-color: var(--success);
}

.modern-step-label {
  font-weight: 600;
  color: var(--gray-600);
  transition: var(--transition);
}

.modern-step.active .modern-step-label {
  color: var(--primary);
}

.modern-step.completed .modern-step-label {
  color: var(--success);
}

.modern-step-connector {
  position: absolute;
  top: 1.75rem;
  left: calc(50% + 1.75rem);
  right: calc(50% - 1.75rem);
  height: 2px;
  background-color: var(--gray-300);
  z-index: 1;
}

.modern-step:last-child .modern-step-connector {
  display: none;
}

.modern-step.active .modern-step-connector,
.modern-step.completed .modern-step-connector {
  background-color: var(--primary);
}

/* Content sections */
.modern-content {
  padding: 2rem;
  border-radius: var(--radius-md);
  background-color: var(--gray-100);
  margin-bottom: 2rem;
}

.modern-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: 1.5rem;
}

/* Alert boxes */
.modern-alert {
  padding: 1.25rem;
  border-radius: var(--radius);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: flex-start;
}

.modern-alert-icon {
  flex-shrink: 0;
  margin-right: 1rem;
  width: 1.5rem;
  height: 1.5rem;
}

.modern-alert-content {
  flex: 1;
}

.modern-alert-content p {
  margin: 0;
  font-weight: 500;
}

.modern-alert-info {
  background-color: var(--info-light);
  color: var(--info);
}

.modern-alert-success {
  background-color: var(--success-light);
  color: var(--success);
}

.modern-alert-warning {
  background-color: var(--warning-light);
  color: var(--dark);
}

.modern-alert-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

/* Requirements list */
.modern-requirements {
  margin-top: 1.5rem;
}

.modern-requirements h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: 1rem;
}

.modern-requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.modern-requirements-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--gray-200);
}

.modern-requirements-item:last-child {
  border-bottom: none;
}

.modern-requirements-icon {
  flex-shrink: 0;
  margin-right: 1rem;
  color: var(--primary);
}

/* Video container */
.modern-video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 75%;
  background-color: black;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  margin-bottom: 1.5rem;
}

.modern-video-container.active {
  box-shadow: 0 0 0 3px var(--primary), var(--shadow-md);
}

.modern-video-container.success {
  box-shadow: 0 0 0 3px var(--success), var(--shadow-md);
}

.modern-video-container.capturing {
  animation: pulse-capture 1.5s infinite;
}

@keyframes pulse-capture {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

.modern-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modern-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Status indicator */
.modern-status {
  padding: 1rem;
  border-radius: var(--radius);
  margin: 1.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.modern-status-info {
  background-color: var(--info-light);
  color: var(--info);
}

.modern-status-success {
  background-color: var(--success-light);
  color: var(--success);
}

.modern-status-warning {
  background-color: var(--warning-light);
  color: var(--dark);
}

.modern-status-error {
  background-color: var(--danger-light);
  color: var(--danger);
}

/* Buttons */
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition);
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  text-transform: none;
  letter-spacing: 0;
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modern-btn-icon {
  margin-right: 0.5rem;
  width: 1.25rem;
  height: 1.25rem;
}

.modern-btn-primary {
  background-color: var(--primary);
  color: white;
}

.modern-btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.modern-btn-primary:active:not(:disabled) {
  transform: translateY(0);
}

.modern-btn-secondary {
  background-color: white;
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.modern-btn-secondary:hover:not(:disabled) {
  background-color: var(--gray-100);
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.modern-btn-success {
  background-color: var(--success);
  color: white;
}

.modern-btn-success:hover:not(:disabled) {
  background-color: var(--secondary-dark);
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

/* Result section */
.modern-result {
  text-align: center;
  padding: 2rem;
}

.modern-result-icon {
  width: 5rem;
  height: 5rem;
  margin: 0 auto 1.5rem;
  background-color: var(--success-light);
  color: var(--success);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-result h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: 1rem;
}

.modern-result p {
  font-size: 1.1rem;
  color: var(--gray-600);
  max-width: 500px;
  margin: 0 auto 2rem;
}

/* Captured image */
.modern-captured-image {
  width: 8rem;
  height: 8rem;
  border-radius: var(--radius-full);
  overflow: hidden;
  margin: 0 auto 1.5rem;
  border: 3px solid var(--primary);
  box-shadow: var(--shadow-md);
}

.modern-captured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modern-face-container {
    padding: 1rem;
  }
  
  .modern-card-header,
  .modern-card-body,
  .modern-content {
    padding: 1.5rem;
  }
  
  .modern-step-label {
    font-size: 0.8rem;
  }
  
  .modern-step-circle {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
  
  .modern-step-connector {
    top: 1.25rem;
    left: calc(50% + 1.25rem);
    right: calc(50% - 1.25rem);
  }
}
