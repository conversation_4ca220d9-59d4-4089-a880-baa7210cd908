/* Course Builder Styles */

/* Section and Lesson Cards */
.section-item {
    transition: all 0.3s ease;
}

.section-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.section-header {
    cursor: pointer;
    transition: all 0.3s ease;
}

.section-header:hover {
    background-color: rgba(55, 65, 81, 1);
}

.lesson-item {
    transition: all 0.3s ease;
}

.lesson-item:hover {
    background-color: rgba(17, 24, 39, 1);
    transform: translateX(5px);
}

/* Drag and Drop Styles */
.sortable-ghost {
    opacity: 0.5;
    background: rgba(59, 130, 246, 0.2);
}

.sortable-chosen {
    background: rgba(59, 130, 246, 0.1);
}

.sortable-drag {
    background: rgba(59, 130, 246, 0.3);
}

/* Modal Animations */
.modal-enter {
    animation: modalEnter 0.3s ease-out;
}

.modal-exit {
    animation: modalExit 0.3s ease-in;
}

@keyframes modalEnter {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes modalExit {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
}

/* Content Type Icons */
.content-type-video {
    color: #ef4444;
}

.content-type-text {
    color: #10b981;
}

.content-type-pdf {
    color: #f59e0b;
}

.content-type-quiz {
    color: #8b5cf6;
}

.content-type-assignment {
    color: #06b6d4;
}

/* Progress Indicators */
.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    transition: stroke-dashoffset 0.35s;
    transform-origin: 50% 50%;
}

/* Status Badges */
.status-published {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-draft {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.status-free {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

/* Action Buttons */
.action-btn {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-item {
        margin-bottom: 1rem;
    }
    
    .lesson-item {
        padding: 0.75rem;
    }
    
    .grid-cols-3 {
        grid-template-columns: 1fr;
    }
    
    .grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.message-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    animation: slideIn 0.3s ease-out;
}

.message-error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Form Enhancements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #f9fafb;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    background-color: #374151;
    color: #f9fafb;
    border: 1px solid #4b5563;
    border-radius: 0.5rem;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.fade-out {
    animation: fadeOut 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
