/* About Page Styles */

/* Hero Section Animation */
.hero-text {
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card Hover Effects */
.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Value Icon Pulse Animation */
.value-icon {
  position: relative;
}

.value-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  z-index: -1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

/* Dark Mode Adjustments */
.dark .feature-card {
  background-color: #1e293b;
  border: 1px solid #334155;
}

.dark .value-card {
  background-color: #1e293b;
  border: 1px solid #334155;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .hero-section {
    padding: 4rem 1rem;
  }
  
  .hero-section h1 {
    font-size: 2.5rem;
  }
  
  .hero-section p {
    font-size: 1.25rem;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .value-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 640px) {
  .value-grid {
    grid-template-columns: 1fr;
  }
  
  .cta-buttons {
    flex-direction: column;
  }
  
  .cta-buttons a {
    width: 100%;
    margin-bottom: 1rem;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #38bdf8;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0284c7;
}

/* Custom Selection */
::selection {
  background: rgba(56, 189, 248, 0.3);
  color: inherit;
}

.dark ::selection {
  background: rgba(56, 189, 248, 0.5);
}
