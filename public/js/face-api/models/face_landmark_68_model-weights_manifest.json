[{"weights": [{"name": "dense0/conv0/filters", "shape": [3, 3, 3, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004853619781194949, "min": -0.5872879935245888}}, {"name": "dense0/conv0/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004396426443960153, "min": -0.7298067896973853}}, {"name": "dense0/conv1/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00635151559231328, "min": -0.5589333721235686}}, {"name": "dense0/conv1/pointwise_filter", "shape": [1, 1, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.009354315552057004, "min": -1.2628325995276957}}, {"name": "dense0/conv1/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0029380727048013726, "min": -0.5846764682554731}}, {"name": "dense0/conv2/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0049374802439820535, "min": -0.6171850304977566}}, {"name": "dense0/conv2/pointwise_filter", "shape": [1, 1, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.009941946758943446, "min": -1.3421628124573652}}, {"name": "dense0/conv2/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0030300481062309416, "min": -0.5272283704841838}}, {"name": "dense0/conv3/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005672684837790097, "min": -0.7431217137505026}}, {"name": "dense0/conv3/pointwise_filter", "shape": [1, 1, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010712201455060173, "min": -1.5639814124387852}}, {"name": "dense0/conv3/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0030966934035806097, "min": -0.3839899820439956}}, {"name": "dense1/conv0/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0039155554537679636, "min": -0.48161332081345953}}, {"name": "dense1/conv0/pointwise_filter", "shape": [1, 1, 32, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01023082966898002, "min": -1.094698774580862}}, {"name": "dense1/conv0/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0027264176630506327, "min": -0.3871513081531898}}, {"name": "dense1/conv1/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004583378632863362, "min": -0.5454220573107401}}, {"name": "dense1/conv1/pointwise_filter", "shape": [1, 1, 64, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00915846403907327, "min": -1.117332612766939}}, {"name": "dense1/conv1/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.003091680419211294, "min": -0.5966943209077797}}, {"name": "dense1/conv2/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005407439727409214, "min": -0.708374604290607}}, {"name": "dense1/conv2/pointwise_filter", "shape": [1, 1, 64, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00946493943532308, "min": -1.2399070660273235}}, {"name": "dense1/conv2/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004409168514550901, "min": -0.9788354102303}}, {"name": "dense1/conv3/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004478132958505668, "min": -0.6493292789833219}}, {"name": "dense1/conv3/pointwise_filter", "shape": [1, 1, 64, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.011063695888893277, "min": -1.2501976354449402}}, {"name": "dense1/conv3/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.003909627596537272, "min": -0.6646366914113363}}, {"name": "dense2/conv0/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.003213915404151468, "min": -0.3374611174359041}}, {"name": "dense2/conv0/pointwise_filter", "shape": [1, 1, 64, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010917326048308728, "min": -1.4520043644250609}}, {"name": "dense2/conv0/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.002800439152063108, "min": -0.38085972468058266}}, {"name": "dense2/conv1/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0050568851770139206, "min": -0.6927932692509071}}, {"name": "dense2/conv1/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01074961213504567, "min": -1.3222022926106174}}, {"name": "dense2/conv1/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0030654204242369708, "min": -0.5487102559384177}}, {"name": "dense2/conv2/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00591809165244009, "min": -0.917304206128214}}, {"name": "dense2/conv2/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01092823346455892, "min": -1.366029183069865}}, {"name": "dense2/conv2/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.002681120470458386, "min": -0.36463238398234055}}, {"name": "dense2/conv3/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0048311497650894465, "min": -0.5797379718107336}}, {"name": "dense2/conv3/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.011227761062921263, "min": -1.4483811771168429}}, {"name": "dense2/conv3/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0034643323982463162, "min": -0.3360402426298927}}, {"name": "dense3/conv0/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.003394978887894574, "min": -0.49227193874471326}}, {"name": "dense3/conv0/pointwise_filter", "shape": [1, 1, 128, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010051267287310432, "min": -1.2765109454884247}}, {"name": "dense3/conv0/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.003142924752889895, "min": -0.4588670139219247}}, {"name": "dense3/conv1/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00448304671867221, "min": -0.5872791201460595}}, {"name": "dense3/conv1/pointwise_filter", "shape": [1, 1, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.016063522357566685, "min": -2.3613377865623026}}, {"name": "dense3/conv1/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00287135781026354, "min": -0.47664539650374765}}, {"name": "dense3/conv2/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006002906724518421, "min": -0.7923836876364315}}, {"name": "dense3/conv2/pointwise_filter", "shape": [1, 1, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.017087187019048954, "min": -1.6061955797906016}}, {"name": "dense3/conv2/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.003124481205846749, "min": -0.46242321846531886}}, {"name": "dense3/conv3/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006576311588287353, "min": -1.0193282961845398}}, {"name": "dense3/conv3/pointwise_filter", "shape": [1, 1, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.015590153955945782, "min": -1.99553970636106}}, {"name": "dense3/conv3/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004453541601405424, "min": -0.6546706154065973}}, {"name": "fc/weights", "shape": [256, 136], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010417488509533453, "min": -1.500118345372817}}, {"name": "fc/bias", "shape": [136], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0025084222648658005, "min": 0.07683877646923065}}], "paths": ["face_landmark_68_model-shard1"]}]