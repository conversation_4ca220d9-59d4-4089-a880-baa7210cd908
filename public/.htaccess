<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Force HTTPS
    RewriteCond %{HTTPS} !=on
    RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle X-XSRF-Token Header
    RewriteCond %{HTTP:x-xsrf-token} .
    RewriteRule .* - [E=HTTP_X_XSRF_TOKEN:%{HTTP:X-XSRF-Token}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Block access to sensitive files
    RewriteRule ^(.*/)?\.git+ - [F,L]
    RewriteRule ^(.*/)?\.env+ - [F,L]
    RewriteRule ^(.*/)?\.htaccess+ - [F,L]
    RewriteRule ^(.*/)?composer\.(json|lock)$ - [F,L]
    RewriteRule ^(.*/)?package(-lock)?\.json$ - [F,L]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# Disable directory browsing
<IfModule mod_autoindex.c>
    Options -Indexes
</IfModule>

# Protect against common vulnerabilities
<IfModule mod_headers.c>
    # Protect against XSS attacks
    Header set X-XSS-Protection "1; mode=block"

    # Protect against content-type sniffing
    Header set X-Content-Type-Options "nosniff"

    # Protect against clickjacking
    Header set X-Frame-Options "SAMEORIGIN"

    # Enable HTTP Strict Transport Security (HSTS)
    Header set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

    # Control cross-origin resource sharing
    Header set Access-Control-Allow-Origin "same-origin"

    # Permissions policy
    Header set Permissions-Policy "camera=(), microphone=(), geolocation=(), interest-cohort=()"

    # Referrer policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"

    # Remove server information
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Disable server signature
ServerSignature Off

# Protect sensitive files
<FilesMatch "^\.(?!well-known\/).*|composer\.(json|lock)|package(-lock)?\.json|\.env|\.gitignore|\.htaccess">
    <IfModule mod_authz_core.c>
        Require all denied
    </IfModule>
    <IfModule !mod_authz_core.c>
        Order allow,deny
        Deny from all
    </IfModule>
</FilesMatch>

# Limit request methods
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} ^(TRACE|TRACK|OPTIONS|HEAD)
    RewriteRule .* - [F]
</IfModule>

# Increase upload limits
<IfModule mod_php.c>
    php_value upload_max_filesize 64M
    php_value post_max_size 64M
    php_value max_execution_time 300
    php_value max_input_time 300
</IfModule>
